/**
 * Simple test script to verify the refresh functionality
 * This can be run in the browser console to test the API calls
 */

// Test function to simulate the refreshCustomerProfile functionality
async function testRefreshCustomerProfile(customerId) {
    console.log('Testing refreshCustomerProfile functionality...');
    
    try {
        const response = await fetch(`http://localhost:8000/customer/api/customers/${customerId}/`, {
            credentials: 'include'
        });

        if (response.ok) {
            const customerData = await response.json();
            console.log('✓ Customer profile data fetched successfully:', customerData);
            return customerData;
        } else {
            console.error('✗ Failed to fetch customer data:', response.status);
            return null;
        }
    } catch (error) {
        console.error('✗ Error fetching customer data:', error);
        return null;
    }
}

// Test function to simulate the refreshCustomerTags functionality
async function testRefreshCustomerTags(customerId, accessToken) {
    console.log('Testing refreshCustomerTags functionality...');
    
    try {
        // First, fetch fresh customer data (including tags)
        const customerResponse = await fetch(`http://localhost:8000/customer/api/customers/${customerId}/`, {
            credentials: 'include'
        });

        if (!customerResponse.ok) {
            console.error('✗ Failed to fetch customer data:', customerResponse.status);
            return null;
        }

        const customerData = await customerResponse.json();
        console.log('✓ Customer data with tags fetched successfully:', customerData);

        // Then, fetch available tags for the modal
        const tagsResponse = await fetch(`http://localhost:8000/api/filters/tags/`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (tagsResponse.ok) {
            const tagsData = await tagsResponse.json();
            console.log('✓ Available tags fetched successfully:', tagsData);
            return { customer: customerData, availableTags: tagsData };
        } else {
            console.error('✗ Failed to fetch available tags:', tagsResponse.status);
            return { customer: customerData, availableTags: null };
        }
    } catch (error) {
        console.error('✗ Error in refreshCustomerTags:', error);
        return null;
    }
}

// Usage instructions
console.log(`
To test the refresh functionality:

1. Open the browser console on the chat center page
2. Copy and paste this script
3. Run the test functions:

   // Test customer profile refresh (replace 123 with actual customer ID)
   testRefreshCustomerProfile(123);

   // Test customer tags refresh (replace with actual customer ID and access token)
   testRefreshCustomerTags(123, 'your-access-token');

4. Check the console output for success/error messages
`);
